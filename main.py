"""
Legacy main entry point - redirects to new structure.
For development, use: uv run python -m novel_parser.main
"""
import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import and run the new main
from novel_parser.main import main

if __name__ == '__main__':
    print("Note: Using legacy main.py. Consider using 'uv run python -m novel_parser.main' instead.")
    main()

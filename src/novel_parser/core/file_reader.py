"""
File reading utilities with byte offset support for efficient chapter content access.
"""
import hashlib
import logging
from pathlib import Path
from typing import Optional, Tuple

logger = logging.getLogger(__name__)


class FileReader:
    """
    Efficient file reader that supports byte offset-based content access.
    Preserves original files and reads content directly using byte offsets.
    """

    @staticmethod
    def calculate_file_hash(file_path: Path) -> str:
        """
        Calculate SHA-256 hash of a file for integrity checking.
        
        Args:
            file_path: Path to the file
            
        Returns:
            str: Hexadecimal hash string
        """
        hash_sha256 = hashlib.sha256()
        try:
            with open(file_path, 'rb') as f:
                # Read file in chunks to handle large files efficiently
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating hash for {file_path}: {e}")
            return ""

    @staticmethod
    def read_content_by_offset(file_path: Path, start_offset: int, end_offset: int) -> str:
        """
        Read content from file using byte offsets.
        
        Args:
            file_path: Path to the file
            start_offset: Starting byte position
            end_offset: Ending byte position
            
        Returns:
            str: Content between the specified offsets
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(start_offset)
                content_bytes = end_offset - start_offset
                content = f.read(content_bytes)
                return content.strip()
        except Exception as e:
            logger.error(f"Error reading content from {file_path} at offset {start_offset}-{end_offset}: {e}")
            return ""

    @staticmethod
    def get_file_info(file_path: Path) -> Tuple[int, str]:
        """
        Get file size and hash for tracking changes.
        
        Args:
            file_path: Path to the file
            
        Returns:
            tuple: (file_size, file_hash)
        """
        try:
            file_size = file_path.stat().st_size
            file_hash = FileReader.calculate_file_hash(file_path)
            return file_size, file_hash
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            return 0, ""

    @staticmethod
    def read_full_content(file_path: Path) -> str:
        """
        Read the entire file content. Used during initial parsing.
        
        Args:
            file_path: Path to the file
            
        Returns:
            str: Full file content
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading full content from {file_path}: {e}")
            return ""

    @staticmethod
    def find_byte_offsets(content: str, chapter_positions: list) -> list:
        """
        Convert line-based chapter positions to byte offsets.
        
        Args:
            content: Full file content
            chapter_positions: List of (line_number, chapter_title) tuples
            
        Returns:
            list: List of dictionaries with chapter info and byte offsets
        """
        lines = content.split('\n')
        chapters_with_offsets = []
        
        # Convert content to bytes to calculate accurate offsets
        content_bytes = content.encode('utf-8')
        
        for i, (line_num, chapter_title) in enumerate(chapter_positions):
            # Calculate start offset (beginning of content after chapter title)
            start_line = line_num + 1
            start_offset = len('\n'.join(lines[:start_line]).encode('utf-8'))
            if start_line > 0:
                start_offset += 1  # Add newline character
            
            # Calculate end offset (start of next chapter or end of file)
            if i < len(chapter_positions) - 1:
                end_line = chapter_positions[i + 1][0]
                end_offset = len('\n'.join(lines[:end_line]).encode('utf-8'))
            else:
                end_offset = len(content_bytes)
            
            chapters_with_offsets.append({
                'title': chapter_title,
                'start_offset': start_offset,
                'end_offset': end_offset,
                'line_start': start_line,
                'line_end': end_line if i < len(chapter_positions) - 1 else len(lines)
            })
        
        return chapters_with_offsets


class IncrementalParser:
    """
    Handles incremental parsing by comparing file changes and updating only modified chapters.
    """

    @staticmethod
    def detect_changes(file_path: Path, stored_hash: str, stored_size: int) -> bool:
        """
        Detect if a file has been modified since last parsing.
        
        Args:
            file_path: Path to the file
            stored_hash: Previously stored file hash
            stored_size: Previously stored file size
            
        Returns:
            bool: True if file has been modified
        """
        try:
            current_size = file_path.stat().st_size
            if current_size != stored_size:
                return True
            
            current_hash = FileReader.calculate_file_hash(file_path)
            return current_hash != stored_hash
        except Exception as e:
            logger.error(f"Error detecting changes for {file_path}: {e}")
            return True  # Assume changed if we can't determine

    @staticmethod
    def find_modified_chapters(old_chapters: list, new_chapters: list) -> Tuple[list, list, list]:
        """
        Compare old and new chapter lists to find modifications.
        
        Args:
            old_chapters: List of existing chapter dictionaries
            new_chapters: List of newly parsed chapter dictionaries
            
        Returns:
            tuple: (added_chapters, modified_chapters, deleted_chapter_ids)
        """
        # Create lookup dictionaries for comparison
        old_by_title = {ch['title']: ch for ch in old_chapters}
        new_by_title = {ch['title']: ch for ch in new_chapters}
        
        added_chapters = []
        modified_chapters = []
        deleted_chapter_ids = []
        
        # Find added and modified chapters
        for title, new_chapter in new_by_title.items():
            if title not in old_by_title:
                added_chapters.append(new_chapter)
            else:
                old_chapter = old_by_title[title]
                # Compare content by offset ranges
                if (new_chapter['start_offset'] != old_chapter.get('start_offset') or
                    new_chapter['end_offset'] != old_chapter.get('end_offset')):
                    new_chapter['id'] = old_chapter['id']  # Preserve chapter ID
                    modified_chapters.append(new_chapter)
        
        # Find deleted chapters
        for title, old_chapter in old_by_title.items():
            if title not in new_by_title:
                deleted_chapter_ids.append(old_chapter['id'])
        
        return added_chapters, modified_chapters, deleted_chapter_ids

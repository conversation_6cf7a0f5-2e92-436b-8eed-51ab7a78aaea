"""
Hash utilities for file integrity checking and change detection.
"""
import hashlib
import logging
from pathlib import Path
from typing import Dict, Optional

logger = logging.getLogger(__name__)


class HashManager:
    """
    Manages file hashes for change detection and integrity verification.
    """

    def __init__(self):
        self._hash_cache: Dict[str, str] = {}

    def get_file_hash(self, file_path: Path, use_cache: bool = True) -> str:
        """
        Get SHA-256 hash of a file with optional caching.
        
        Args:
            file_path: Path to the file
            use_cache: Whether to use cached hash if available
            
        Returns:
            str: Hexadecimal hash string
        """
        file_str = str(file_path)
        
        if use_cache and file_str in self._hash_cache:
            return self._hash_cache[file_str]
        
        hash_value = self._calculate_hash(file_path)
        self._hash_cache[file_str] = hash_value
        return hash_value

    def _calculate_hash(self, file_path: Path) -> str:
        """
        Calculate SHA-256 hash of a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            str: Hexadecimal hash string
        """
        hash_sha256 = hashlib.sha256()
        try:
            with open(file_path, 'rb') as f:
                # Read file in chunks for memory efficiency
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating hash for {file_path}: {e}")
            return ""

    def has_file_changed(self, file_path: Path, stored_hash: str) -> bool:
        """
        Check if a file has changed by comparing hashes.
        
        Args:
            file_path: Path to the file
            stored_hash: Previously stored hash
            
        Returns:
            bool: True if file has changed
        """
        current_hash = self.get_file_hash(file_path, use_cache=False)
        return current_hash != stored_hash

    def invalidate_cache(self, file_path: Optional[Path] = None):
        """
        Invalidate hash cache for a specific file or all files.
        
        Args:
            file_path: Specific file to invalidate, or None for all files
        """
        if file_path:
            file_str = str(file_path)
            self._hash_cache.pop(file_str, None)
        else:
            self._hash_cache.clear()

    def get_content_hash(self, content: str) -> str:
        """
        Calculate hash of text content.
        
        Args:
            content: Text content to hash
            
        Returns:
            str: Hexadecimal hash string
        """
        return hashlib.sha256(content.encode('utf-8')).hexdigest()

    def batch_hash_files(self, file_paths: list) -> Dict[str, str]:
        """
        Calculate hashes for multiple files efficiently.
        
        Args:
            file_paths: List of file paths
            
        Returns:
            dict: Mapping of file path to hash
        """
        results = {}
        for file_path in file_paths:
            path_obj = Path(file_path) if isinstance(file_path, str) else file_path
            results[str(path_obj)] = self.get_file_hash(path_obj, use_cache=False)
        return results


# Global hash manager instance
hash_manager = HashManager()

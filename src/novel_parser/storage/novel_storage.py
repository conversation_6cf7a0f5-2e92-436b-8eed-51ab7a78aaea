"""
Modern storage layer with byte offset indexing and incremental updates.
Preserves original files and uses byte offsets for efficient content access.
"""
import sqlite3
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from ..core.file_reader import FileReader
from ..models.base import NovelMetadata, ChapterMetadata

logger = logging.getLogger(__name__)


class NovelStorage:
    """
    Modern storage system that uses byte offsets instead of storing full content.
    Provides efficient incremental updates and preserves original files.
    """

    def __init__(self, db_path: str = 'data/novels.db'):
        """
        Initialize the storage with database path.

        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_db()

    def _init_db(self):
        """Initialize the database with modern schema supporting byte offsets."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create novels table with file tracking
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS novels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            author TEXT,
            description TEXT,
            file_path TEXT UNIQUE NOT NULL,
            file_size INTEGER NOT NULL,
            file_hash TEXT NOT NULL,
            chapter_count INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create chapters table with byte offsets (no content storage)
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS chapters (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            novel_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            start_offset INTEGER NOT NULL,
            end_offset INTEGER NOT NULL,
            chapter_index INTEGER NOT NULL,
            content_hash TEXT,
            FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE
        )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_novels_file_path ON novels(file_path)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_chapters_novel_id ON chapters(novel_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_chapters_index ON chapters(novel_id, chapter_index)')

        # Create search index for titles and authors only (no content)
        cursor.execute('''
        CREATE VIRTUAL TABLE IF NOT EXISTS novel_search
        USING fts5(title, author, novel_id UNINDEXED, tokenize='unicode61')
        ''')

        conn.commit()
        conn.close()
        logger.info(f"Database initialized at {self.db_path}")

    def save_novel(self, novel_data: NovelMetadata) -> Optional[int]:
        """
        Save a novel with byte offset indexing.

        Args:
            novel_data: Novel metadata with chapters and byte offsets

        Returns:
            int: Novel ID if successful, None otherwise
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            # Check if novel already exists
            cursor.execute("SELECT id FROM novels WHERE file_path = ?", (novel_data.file_path,))
            existing = cursor.fetchone()

            if existing:
                # Update existing novel
                novel_id = existing['id']
                cursor.execute('''
                UPDATE novels
                SET title = ?, author = ?, description = ?, file_size = ?,
                    file_hash = ?, chapter_count = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                ''', (novel_data.title, novel_data.author, novel_data.description,
                      novel_data.file_size, novel_data.file_hash,
                      novel_data.chapter_count, novel_id))

                # Delete existing chapters
                cursor.execute("DELETE FROM chapters WHERE novel_id = ?", (novel_id,))
                cursor.execute("DELETE FROM novel_search WHERE novel_id = ?", (novel_id,))
            else:
                # Insert new novel
                cursor.execute('''
                INSERT INTO novels (title, author, description, file_path, file_size,
                                  file_hash, chapter_count)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (novel_data.title, novel_data.author, novel_data.description,
                      novel_data.file_path, novel_data.file_size,
                      novel_data.file_hash, novel_data.chapter_count))
                novel_id = cursor.lastrowid

            # Insert chapters with byte offsets
            for chapter in novel_data.chapters:
                cursor.execute('''
                INSERT INTO chapters (novel_id, title, start_offset, end_offset,
                                    chapter_index, content_hash)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', (novel_id, chapter.title, chapter.start_offset,
                      chapter.end_offset, chapter.chapter_index, chapter.content_hash))

            # Add to search index (title and author only)
            cursor.execute('''
            INSERT INTO novel_search (title, author, novel_id)
            VALUES (?, ?, ?)
            ''', (novel_data.title, novel_data.author or '', novel_id))

            conn.commit()
            logger.info(f"Saved novel: {novel_data.title} with {len(novel_data.chapters)} chapters")
            return novel_id

        except Exception as e:
            conn.rollback()
            logger.error(f"Error saving novel {novel_data.title}: {str(e)}")
            return None
        finally:
            conn.close()

    def get_novel_by_path(self, file_path: str) -> Optional[Dict]:
        """
        Get novel metadata by file path.

        Args:
            file_path: Path to the novel file

        Returns:
            dict: Novel metadata or None if not found
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            cursor.execute('''
            SELECT id, title, author, description, file_path, file_size,
                   file_hash, chapter_count, created_at, updated_at
            FROM novels WHERE file_path = ?
            ''', (file_path,))

            row = cursor.fetchone()
            if row:
                return dict(row)
            return None

        except Exception as e:
            logger.error(f"Error getting novel by path {file_path}: {e}")
            return None
        finally:
            conn.close()

    def search_novels(self, query: str = "") -> List[Dict]:
        """
        Search novels by title or author.

        Args:
            query: Search query string

        Returns:
            list: List of novel dictionaries
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            if query.strip():
                # Use FTS search for non-empty queries
                cursor.execute('''
                SELECT n.id, n.title, n.author, n.description, n.file_path,
                       n.chapter_count, n.file_size, n.created_at, n.updated_at,
                       (SELECT c.title FROM chapters c
                        WHERE c.novel_id = n.id
                        ORDER BY c.chapter_index DESC LIMIT 1) as last_chapter
                FROM novels n
                JOIN novel_search ns ON n.id = ns.novel_id
                WHERE novel_search MATCH ?
                ORDER BY n.updated_at DESC
                ''', (query,))
            else:
                # Return all novels for empty query
                cursor.execute('''
                SELECT n.id, n.title, n.author, n.description, n.file_path,
                       n.chapter_count, n.file_size, n.created_at, n.updated_at,
                       (SELECT c.title FROM chapters c
                        WHERE c.novel_id = n.id
                        ORDER BY c.chapter_index DESC LIMIT 1) as last_chapter
                FROM novels n
                ORDER BY n.updated_at DESC
                ''')

            results = []
            for row in cursor.fetchall():
                novel_dict = dict(row)
                novel_dict['cover_url'] = '/static/book_cover.jpg'
                results.append(novel_dict)

            return results

        except Exception as e:
            logger.error(f"Error searching novels with query '{query}': {e}")
            return []
        finally:
            conn.close()

    def get_novel_chapters(self, novel_id: int) -> Optional[Dict]:
        """
        Get novel with its chapters list (metadata only).

        Args:
            novel_id: ID of the novel

        Returns:
            dict: Novel with chapters list or None if not found
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            # Get novel metadata
            cursor.execute('''
            SELECT id, title, chapter_count
            FROM novels WHERE id = ?
            ''', (novel_id,))

            novel_row = cursor.fetchone()
            if not novel_row:
                return None

            novel = {
                'id': novel_row['id'],
                'title': novel_row['title'],
                'chapter_count': novel_row['chapter_count'],
                'chapters': []
            }

            # Get chapter metadata (no content)
            cursor.execute('''
            SELECT id, title, chapter_index
            FROM chapters
            WHERE novel_id = ?
            ORDER BY chapter_index
            ''', (novel_id,))

            for row in cursor.fetchall():
                novel['chapters'].append({
                    'id': row['id'],
                    'title': row['title'],
                    'index': row['chapter_index']
                })

            return novel

        except Exception as e:
            logger.error(f"Error getting chapters for novel {novel_id}: {e}")
            return None
        finally:
            conn.close()

    def get_chapter_content(self, chapter_id: int) -> Optional[Dict]:
        """
        Get chapter content using byte offset reading.

        Args:
            chapter_id: ID of the chapter

        Returns:
            dict: Chapter with content or None if not found
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            # Get chapter metadata with novel info
            cursor.execute('''
            SELECT c.id, c.title, c.start_offset, c.end_offset, c.chapter_index,
                   n.id as novel_id, n.title as novel_title, n.file_path
            FROM chapters c
            JOIN novels n ON c.novel_id = n.id
            WHERE c.id = ?
            ''', (chapter_id,))

            row = cursor.fetchone()
            if not row:
                return None

            # Read content using byte offsets
            file_path = Path(row['file_path'])
            content = FileReader.read_content_by_offset(
                file_path, row['start_offset'], row['end_offset']
            )

            return {
                'id': row['id'],
                'title': row['title'],
                'content': content,
                'index': row['chapter_index'],
                'novel_id': row['novel_id'],
                'novel_title': row['novel_title']
            }

        except Exception as e:
            logger.error(f"Error getting content for chapter {chapter_id}: {e}")
            return None
        finally:
            conn.close()

    def delete_novel(self, file_path: str) -> Optional[Tuple[int, str]]:
        """
        Delete a novel from the database.

        Args:
            file_path: Path of the novel file to delete

        Returns:
            tuple: (novel_id, title) if novel was found, None otherwise
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT id, title FROM novels WHERE file_path = ?", (file_path,))
            novel = cursor.fetchone()

            if novel:
                novel_id = novel['id']
                title = novel['title']

                # Delete novel and related data (cascade)
                cursor.execute("DELETE FROM novels WHERE id = ?", (novel_id,))
                cursor.execute("DELETE FROM novel_search WHERE novel_id = ?", (novel_id,))

                conn.commit()
                logger.info(f"Deleted novel: {title}")
                return (novel_id, title)
            else:
                logger.warning(f"Novel not found for deletion: {file_path}")
                return None

        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting novel {file_path}: {e}")
            return None
        finally:
            conn.close()

    def update_novel_path(self, old_path: str, new_path: str) -> bool:
        """
        Update the file path of a novel (for file renames/moves).

        Args:
            old_path: Current file path
            new_path: New file path

        Returns:
            bool: True if update was successful
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
            UPDATE novels
            SET file_path = ?, updated_at = CURRENT_TIMESTAMP
            WHERE file_path = ?
            ''', (new_path, old_path))

            if cursor.rowcount > 0:
                conn.commit()
                logger.info(f"Updated novel path: {old_path} -> {new_path}")
                return True
            else:
                logger.warning(f"No novel found to update path: {old_path}")
                return False

        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating novel path {old_path} -> {new_path}: {e}")
            return False
        finally:
            conn.close()

    def get_statistics(self) -> Dict:
        """
        Get database statistics.

        Returns:
            dict: Statistics about stored novels and chapters
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT COUNT(*) as novel_count FROM novels")
            novel_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) as chapter_count FROM chapters")
            chapter_count = cursor.fetchone()[0]

            cursor.execute('''
            SELECT MAX(updated_at) as last_update
            FROM novels WHERE updated_at IS NOT NULL
            ''')
            last_update = cursor.fetchone()[0]

            return {
                'total_novels': novel_count,
                'total_chapters': chapter_count,
                'last_update': last_update
            }

        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {'total_novels': 0, 'total_chapters': 0, 'last_update': None}
        finally:
            conn.close()

"""
Main entry point for the novel parser system.
"""
import sys
import logging
import threading
import signal
from pathlib import Path
from typing import Optional

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent.parent))

from novel_parser.storage import NovelStorage
from novel_parser.parser import NovelMonitor
from novel_parser.api import create_app

# Global variables
storage: Optional[NovelStorage] = None
monitor: Optional[NovelMonitor] = None
monitor_thread: Optional[threading.Thread] = None
shutdown_event = threading.Event()


def setup_logging():
    """Set up logging configuration."""
    # Create logs directory
    logs_dir = Path('logs')
    logs_dir.mkdir(exist_ok=True)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(logs_dir / 'novel_system.log')
        ]
    )

    # Set specific log levels
    logging.getLogger('watchdog').setLevel(logging.WARNING)
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)


def setup_directories():
    """Create necessary directories."""
    directories = ['logs', 'data', 'docs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)


def start_monitor():
    """Start the file monitor in a separate thread."""
    global storage, monitor, monitor_thread

    logger = logging.getLogger(__name__)
    logger.info("Starting Novel Parser System...")

    try:
        # Create storage
        storage = NovelStorage(db_path='data/novels.db')

        # Create monitor
        monitor = NovelMonitor(['docs'], storage)

        # Start monitor in separate thread
        monitor_thread = threading.Thread(target=monitor.start, daemon=True)
        monitor_thread.start()

        logger.info("Novel Parser System started successfully")
        return True

    except Exception as e:
        logger.error(f"Failed to start monitor: {e}")
        return False


def stop_monitor():
    """Stop the file monitor."""
    global monitor

    logger = logging.getLogger(__name__)
    logger.info("Shutting down Novel Parser System...")

    if monitor:
        try:
            monitor.stop()
            logger.info("Monitor stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping monitor: {e}")

    shutdown_event.set()
    logger.info("Novel Parser System shut down")


def signal_handler(signum, _frame):
    """Handle shutdown signals."""
    logger = logging.getLogger(__name__)
    logger.info(f"Received signal {signum}, shutting down...")
    stop_monitor()
    sys.exit(0)


def main():
    """Main entry point for the application."""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Set up logging and directories
    setup_logging()
    setup_directories()

    logger = logging.getLogger(__name__)
    logger.info("Novel Parser v2.0 starting...")

    # Start monitor
    if not start_monitor():
        logger.error("Failed to start system, exiting")
        sys.exit(1)

    # Create FastAPI app
    if not storage:
        logger.error("Storage not initialized, exiting")
        sys.exit(1)

    app = create_app(storage)

    # Start server
    logger.info("Starting Novel API server on 0.0.0.0:5001")

    try:
        import uvicorn
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=5001,
            log_level="info",
            access_log=False  # Reduce log noise
        )
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Server error: {e}")
    finally:
        stop_monitor()


def dev_main():
    """Development entry point with auto-reload."""
    setup_logging()
    setup_directories()

    logger = logging.getLogger(__name__)
    logger.info("Starting Novel Parser in development mode...")

    # Start monitor
    if not start_monitor():
        logger.error("Failed to start system, exiting")
        sys.exit(1)

    if not storage:
        logger.error("Storage not initialized, exiting")
        sys.exit(1)

    # Start with auto-reload
    import uvicorn
    uvicorn.run(
        "novel_parser.main:create_dev_app",
        host="0.0.0.0",
        port=5001,
        reload=True,
        log_level="info"
    )


def create_dev_app():
    """Create app for development with auto-reload."""
    if not storage:
        # Initialize storage for development
        global storage
        storage = NovelStorage(db_path='data/novels.db')

    return create_app(storage)


if __name__ == '__main__':
    main()

"""
Base data models for the novel parser system.
"""
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class ChapterInfo(BaseModel):
    """Chapter information for table of contents."""
    id: int
    title: str
    index: int


class NovelInfo(BaseModel):
    """Novel information for search results."""
    id: int
    title: str
    author: Optional[str] = None
    description: Optional[str] = None
    file_path: str
    chapter_count: int
    last_chapter: str = ""
    cover_url: str = "/static/book_cover.jpg"
    file_size: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class NovelChapters(BaseModel):
    """Novel with its chapters list."""
    id: int
    title: str
    chapter_count: int
    chapters: List[ChapterInfo]


class ChapterContent(BaseModel):
    """Chapter content with lazy loading support."""
    id: int
    title: str
    content: str
    index: int
    novel_id: int
    novel_title: str


class SearchResponse(BaseModel):
    """Search response."""
    results: List[NovelInfo]
    total_count: int = Field(default_factory=lambda: 0)
    
    def __init__(self, **data):
        super().__init__(**data)
        if 'total_count' not in data:
            self.total_count = len(self.results)


class StatusResponse(BaseModel):
    """System status response."""
    status: str = "running"
    monitored_directories: List[str] = Field(default_factory=list)
    total_novels: int = 0
    total_chapters: int = 0
    last_scan_time: Optional[datetime] = None


class NovelMetadata(BaseModel):
    """Internal model for novel metadata with file tracking."""
    title: str
    author: Optional[str] = None
    description: Optional[str] = None
    file_path: str
    file_size: int
    file_hash: str
    chapter_count: int
    chapters: List['ChapterMetadata'] = Field(default_factory=list)


class ChapterMetadata(BaseModel):
    """Internal model for chapter metadata with byte offsets."""
    title: str
    start_offset: int
    end_offset: int
    chapter_index: int
    content_hash: Optional[str] = None


# Update forward references
NovelMetadata.model_rebuild()

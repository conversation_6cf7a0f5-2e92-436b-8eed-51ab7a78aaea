"""
Modern FastAPI application with clean endpoints and lazy loading.
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import logging
from typing import Optional
from pathlib import Path

from ..models import (
    SearchResponse,
    NovelChapters,
    ChapterContent,
    StatusResponse,
    NovelInfo,
    ChapterInfo
)
from ..storage.novel_storage import NovelStorage

logger = logging.getLogger(__name__)


def create_app(novel_storage: NovelStorage) -> FastAPI:
    """
    Create FastAPI application with clean, focused endpoints.
    
    Args:
        novel_storage: Storage instance
        
    Returns:
        FastAPI: Configured application
    """
    app = FastAPI(
        title="Novel Parser API",
        description="Modern API for parsing and accessing novel content with byte-offset indexing",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Mount static files
    static_path = Path(__file__).parent.parent / "static"
    if static_path.exists():
        app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
    else:
        logger.warning(f"Static directory not found: {static_path}")

    @app.get("/", summary="API Root")
    async def root():
        """API root endpoint."""
        return {
            "message": "Novel Parser API v2.0",
            "docs": "/docs",
            "status": "/api/status"
        }

    @app.get("/api/status", response_model=StatusResponse, summary="System Status")
    async def get_status():
        """Get system status and statistics."""
        try:
            stats = novel_storage.get_statistics()
            return StatusResponse(
                status="running",
                monitored_directories=["docs"],  # Fixed directory
                total_novels=stats.get('total_novels', 0),
                total_chapters=stats.get('total_chapters', 0),
                last_scan_time=stats.get('last_update')
            )
        except Exception as e:
            logger.error(f"Error getting status: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

    @app.get("/api/novels/search", response_model=SearchResponse, summary="Search Novels")
    async def search_novels(
        q: Optional[str] = Query("", description="Search query for title or author")
    ):
        """
        Search for novels by title or author.
        
        Args:
            q: Search query string (empty returns all novels)
            
        Returns:
            SearchResponse: List of matching novels
        """
        try:
            logger.info(f"Search request: '{q}'")
            
            # Search novels using storage
            results = novel_storage.search_novels(q)
            
            # Convert to NovelInfo objects
            novel_infos = []
            for novel in results:
                novel_info = NovelInfo(**novel)
                novel_infos.append(novel_info)
            
            logger.info(f"Search for '{q}' returned {len(novel_infos)} novels")
            return SearchResponse(results=novel_infos)
            
        except Exception as e:
            logger.error(f"Error searching novels: {e}")
            raise HTTPException(status_code=500, detail="Search failed")

    @app.get("/api/novels/{novel_id}/chapters", response_model=NovelChapters, summary="Get Novel Chapters")
    async def get_novel_chapters(novel_id: int):
        """
        Get chapters list for a novel (metadata only, no content).
        
        Args:
            novel_id: ID of the novel
            
        Returns:
            NovelChapters: Novel with chapters list
        """
        try:
            logger.info(f"Getting chapters for novel {novel_id}")
            
            novel = novel_storage.get_novel_chapters(novel_id)
            if not novel:
                raise HTTPException(status_code=404, detail="Novel not found")
            
            # Convert chapters to ChapterInfo objects
            chapters = [ChapterInfo(**ch) for ch in novel['chapters']]
            
            return NovelChapters(
                id=novel['id'],
                title=novel['title'],
                chapter_count=novel['chapter_count'],
                chapters=chapters
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chapters for novel {novel_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to get chapters")

    @app.get("/api/chapters/{chapter_id}", response_model=ChapterContent, summary="Get Chapter Content")
    async def get_chapter_content(chapter_id: int):
        """
        Get content of a specific chapter using lazy loading.
        
        Args:
            chapter_id: ID of the chapter
            
        Returns:
            ChapterContent: Chapter with content loaded from file
        """
        try:
            logger.info(f"Getting content for chapter {chapter_id}")
            
            chapter = novel_storage.get_chapter_content(chapter_id)
            if not chapter:
                raise HTTPException(status_code=404, detail="Chapter not found")
            
            return ChapterContent(**chapter)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting content for chapter {chapter_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to get chapter content")

    @app.get("/health", summary="Health Check")
    async def health_check():
        """Simple health check endpoint."""
        return {"status": "healthy", "version": "2.0.0"}

    # Error handlers
    @app.exception_handler(404)
    async def not_found_handler(request, exc):
        return {"error": "Not found", "detail": str(exc.detail) if hasattr(exc, 'detail') else "Resource not found"}

    @app.exception_handler(500)
    async def internal_error_handler(request, exc):
        logger.error(f"Internal server error: {exc}")
        return {"error": "Internal server error", "detail": "An unexpected error occurred"}

    return app

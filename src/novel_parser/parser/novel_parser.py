"""
Modern TXT novel parser with byte offset indexing and incremental parsing support.
"""
import re
import logging
from pathlib import Path
from typing import Optional, Tuple, List

from ..core.file_reader import FileReader
from ..core.hash_utils import hash_manager
from ..models.base import NovelMetadata, ChapterMetadata

logger = logging.getLogger(__name__)


class NovelParser:
    """
    Modern parser for TXT novel files with byte offset indexing.
    Supports incremental parsing and preserves original files.
    """

    # Enhanced chapter detection patterns
    CHAPTER_PATTERNS = [
        # 第X章 Title
        r'^第[\d〇零一二两三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾佰仟]+[章节回卷].{0,50}$',

        # 序章、终章、尾声、楔子
        r'^[序终尾楔引前后][章言声子记].{0,50}$',

        # 正文、番外
        r'^(正文|番外).{0,50}$',

        # 上部、中篇、下卷
        r'^[上中下外][部篇卷].{0,50}$',

        # 数字章节 01, 001, etc.
        r'^\d{1,4}[^\.：&].{0,50}$',

        # Chapter1 title
        r'^Chapter.{0,50}$',

        # ☆ special markers
        r'^[☆★].{0,50}$',

        # 【章节】格式
        r'^【.{1,50}】$',

        # 卷一、卷二等
        r'^卷[\d〇零一二两三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾佰仟]+.{0,50}$',
    ]

    def __init__(self):
        """Initialize the parser with compiled regex patterns."""
        self.chapter_regex = re.compile(
            '|'.join(f'({pattern})' for pattern in self.CHAPTER_PATTERNS),
            re.MULTILINE
        )

    def parse_file(self, file_path: Path) -> Optional[NovelMetadata]:
        """
        Parse a novel file and extract metadata with byte offsets.

        Args:
            file_path: Path to the novel file

        Returns:
            NovelMetadata: Novel metadata with chapters and byte offsets
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists() or not file_path.is_file():
                logger.error(f"File not found: {file_path}")
                return None

            # Get file info
            file_size, file_hash = FileReader.get_file_info(file_path)
            if not file_hash:
                logger.error(f"Could not calculate hash for: {file_path}")
                return None

            # Read content for parsing
            content = FileReader.read_full_content(file_path)
            if not content:
                logger.error(f"Could not read content from: {file_path}")
                return None

            # Extract title and author from filename
            novel_title, author = self._extract_title_author(file_path)

            # Extract chapters with byte offsets
            chapters = self._extract_chapters_with_offsets(content)

            # Create novel metadata
            novel_data = NovelMetadata(
                title=novel_title,
                author=author,
                file_path=str(file_path),
                file_size=file_size,
                file_hash=file_hash,
                chapter_count=len(chapters),
                chapters=chapters
            )

            logger.info(f"Successfully parsed novel: {novel_title} with {len(chapters)} chapters")
            return novel_data

        except Exception as e:
            logger.error(f"Error parsing file {file_path}: {str(e)}")
            return None

    def _extract_title_author(self, file_path: Path) -> Tuple[str, Optional[str]]:
        """
        Extract title and author from filename.

        Args:
            file_path: Path to the file

        Returns:
            tuple: (title, author)
        """
        file_name = file_path.stem

        # Check for pattern "title 作者：author"
        author_pattern = re.compile(r'(.+)\s作者：(.+)')
        match = author_pattern.match(file_name)

        if match:
            title = match.group(1).strip()
            author = match.group(2).strip()
            logger.info(f"Extracted title: {title}, author: {author}")
            return title, author

        return file_name, None

    def _extract_chapters_with_offsets(self, content: str) -> List[ChapterMetadata]:
        """
        Extract chapters with byte offset information.

        Args:
            content: Full file content

        Returns:
            list: List of ChapterMetadata with byte offsets
        """
        lines = content.split('\n')
        chapter_positions = []

        # Find chapter headings
        for i, line in enumerate(lines):
            line_clean = line.strip().replace(' ', '')

            if not line_clean:
                continue

            # Check against chapter patterns
            if any(re.fullmatch(pattern, line_clean) for pattern in self.CHAPTER_PATTERNS):
                chapter_positions.append((i, line.strip()))

        # Handle case where no chapters are found
        if not chapter_positions:
            logger.info("No chapter patterns found, treating entire content as single chapter")
            # Check if content starts with non-chapter content (前言等)
            first_lines = '\n'.join(lines[:10]).strip()
            if first_lines and not any(marker in first_lines.lower()
                                     for marker in ['chapter', '章', '节']):
                chapter_positions = [(0, '正文')]
            else:
                chapter_positions = [(0, '全文')]

        # Convert to byte offsets
        chapters = FileReader.find_byte_offsets(content, chapter_positions)

        # Create ChapterMetadata objects
        chapter_metadata = []
        for i, chapter_info in enumerate(chapters):
            # Calculate content hash for change detection
            chapter_content = content[chapter_info['line_start']:chapter_info['line_end']]
            content_hash = hash_manager.get_content_hash(chapter_content)

            chapter_metadata.append(ChapterMetadata(
                title=chapter_info['title'],
                start_offset=chapter_info['start_offset'],
                end_offset=chapter_info['end_offset'],
                chapter_index=i,
                content_hash=content_hash
            ))

        return chapter_metadata

    def can_parse_incrementally(self, file_path: Path, stored_hash: str, stored_size: int) -> bool:
        """
        Check if a file can be parsed incrementally.

        Args:
            file_path: Path to the file
            stored_hash: Previously stored file hash
            stored_size: Previously stored file size

        Returns:
            bool: True if incremental parsing is possible
        """
        try:
            current_size = file_path.stat().st_size

            # For now, we'll do full re-parsing for any changes
            # Future enhancement: implement true incremental parsing
            if current_size != stored_size:
                return False

            current_hash = FileReader.calculate_file_hash(file_path)
            return current_hash == stored_hash

        except Exception as e:
            logger.error(f"Error checking incremental parsing for {file_path}: {e}")
            return False

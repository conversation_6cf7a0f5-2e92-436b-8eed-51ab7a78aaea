"""
Modern file monitoring system with incremental parsing and batch updates.
"""
import time
import logging
from pathlib import Path
from typing import List, Set
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from .novel_parser import NovelParser
from .epub_parser import EpubParser
from ..storage.novel_storage import NovelStorage
from ..core.file_reader import IncrementalParser

logger = logging.getLogger(__name__)


class NovelFileHandler(FileSystemEventHandler):
    """
    Modern file system event handler with incremental parsing support.
    """

    def __init__(self, novel_storage: NovelStorage):
        """
        Initialize the handler with storage.
        
        Args:
            novel_storage: Storage instance
        """
        self.storage = novel_storage
        self.txt_parser = NovelParser()
        self.epub_parser = EpubParser()
        self.incremental_parser = IncrementalParser()
        
        # Supported file extensions and their parsers
        self.supported_extensions = {
            ".txt": self.txt_parser,
            ".epub": self.epub_parser
        }
        
        # Batch processing
        self._pending_files: Set[str] = set()
        self._last_batch_time = time.time()
        self._batch_delay = 2.0  # seconds

    def on_created(self, event):
        """Handle file creation events."""
        if not event.is_directory and self._is_supported_file(event.src_path):
            logger.info(f"File created: {event.src_path}")
            self._schedule_file_processing(event.src_path)

    def on_modified(self, event):
        """Handle file modification events with incremental parsing."""
        if not event.is_directory and self._is_supported_file(event.src_path):
            logger.info(f"File modified: {event.src_path}")
            self._schedule_file_processing(event.src_path, is_modification=True)

    def on_deleted(self, event):
        """Handle file deletion events."""
        if not event.is_directory and self._is_supported_file(event.src_path):
            logger.info(f"File deleted: {event.src_path}")
            result = self.storage.delete_novel(event.src_path)
            if result:
                novel_id, title = result
                logger.info(f"Deleted novel from database: {title}")

    def on_moved(self, event):
        """Handle file move/rename events."""
        if not event.is_directory and self._is_supported_file(event.dest_path):
            logger.info(f"File moved: {event.src_path} -> {event.dest_path}")
            
            # Check if it's a simple rename in the same directory
            if Path(event.src_path).parent == Path(event.dest_path).parent:
                # Update path in database
                success = self.storage.update_novel_path(event.src_path, event.dest_path)
                if success:
                    logger.info(f"Updated novel path in database")
                else:
                    # Fallback: delete old and create new
                    self.storage.delete_novel(event.src_path)
                    self._schedule_file_processing(event.dest_path)
            else:
                # Move between directories: delete old and create new
                self.storage.delete_novel(event.src_path)
                self._schedule_file_processing(event.dest_path)

    def _is_supported_file(self, file_path: str) -> bool:
        """Check if the file has a supported extension."""
        file_ext = Path(file_path).suffix.lower()
        return file_ext in self.supported_extensions

    def _schedule_file_processing(self, file_path: str, is_modification: bool = False):
        """
        Schedule file for batch processing.
        
        Args:
            file_path: Path to the file
            is_modification: Whether this is a modification event
        """
        self._pending_files.add(file_path)
        
        # Process immediately for single files, or batch for multiple
        current_time = time.time()
        if (current_time - self._last_batch_time) > self._batch_delay:
            self._process_pending_files()

    def _process_pending_files(self):
        """Process all pending files in batch."""
        if not self._pending_files:
            return
            
        logger.info(f"Processing batch of {len(self._pending_files)} files")
        
        for file_path in self._pending_files.copy():
            try:
                self._process_novel_file(file_path)
                self._pending_files.discard(file_path)
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                
        self._last_batch_time = time.time()

    def _process_novel_file(self, file_path: str):
        """
        Process a novel file with incremental parsing support.
        
        Args:
            file_path: Path to the file
        """
        file_path_obj = Path(file_path)
        file_ext = file_path_obj.suffix.lower()
        
        if file_ext not in self.supported_extensions:
            logger.warning(f"Unsupported file extension: {file_ext} for file: {file_path}")
            return
            
        parser = self.supported_extensions[file_ext]
        
        # Check if file exists in database for incremental parsing
        existing_novel = self.storage.get_novel_by_path(file_path)
        
        if existing_novel:
            # Check if incremental parsing is possible
            if hasattr(parser, 'can_parse_incrementally'):
                can_incremental = parser.can_parse_incrementally(
                    file_path_obj, 
                    existing_novel['file_hash'], 
                    existing_novel['file_size']
                )
                
                if can_incremental:
                    logger.info(f"No changes detected in {file_path}, skipping parsing")
                    return
        
        # Parse the file (full parsing for now)
        logger.info(f"Parsing file: {file_path}")
        novel_data = parser.parse_file(file_path_obj)
        
        if novel_data:
            novel_id = self.storage.save_novel(novel_data)
            if novel_id:
                action = "Updated" if existing_novel else "Added"
                logger.info(f"{action} novel: {novel_data.title}")
            else:
                logger.error(f"Failed to save novel: {novel_data.title}")
        else:
            logger.error(f"Failed to parse file: {file_path}")

    def force_process_pending(self):
        """Force processing of all pending files."""
        self._process_pending_files()


class NovelMonitor:
    """
    Modern file monitor with batch processing and incremental parsing.
    """

    def __init__(self, novel_dirs: List[str], novel_storage: NovelStorage):
        """
        Initialize the monitor.
        
        Args:
            novel_dirs: List of directories to monitor
            novel_storage: Storage instance
        """
        self.novel_dirs = [Path(d).resolve() for d in novel_dirs]
        self.storage = novel_storage
        self.observer = Observer()
        self.handler = NovelFileHandler(novel_storage)
        self._is_running = False

    def start(self):
        """Start monitoring directories."""
        logger.info("Starting novel file monitor...")
        
        # Scan existing files first
        self._scan_existing_files()
        
        # Set up directory monitoring
        for novel_dir in self.novel_dirs:
            if novel_dir.exists():
                self.observer.schedule(self.handler, str(novel_dir), recursive=True)
                logger.info(f"Monitoring directory: {novel_dir}")
            else:
                logger.warning(f"Directory does not exist: {novel_dir}")
        
        # Start observer
        self.observer.start()
        self._is_running = True
        logger.info("Novel file monitor started successfully")
        
        try:
            while self._is_running:
                time.sleep(1)
                # Process any pending files periodically
                self.handler.force_process_pending()
        except KeyboardInterrupt:
            self.stop()

    def stop(self):
        """Stop monitoring."""
        logger.info("Stopping novel file monitor...")
        self._is_running = False
        
        # Process any remaining pending files
        self.handler.force_process_pending()
        
        # Stop observer
        self.observer.stop()
        self.observer.join()
        logger.info("Novel file monitor stopped")

    def _scan_existing_files(self):
        """Scan existing files in monitored directories."""
        logger.info("Scanning existing files...")
        
        for novel_dir in self.novel_dirs:
            if not novel_dir.exists():
                continue
                
            logger.info(f"Scanning directory: {novel_dir}")
            
            # Scan for TXT files
            txt_files = list(novel_dir.glob('**/*.txt'))
            logger.info(f"Found {len(txt_files)} TXT files")
            
            for file_path in txt_files:
                try:
                    self.handler._process_novel_file(str(file_path))
                except Exception as e:
                    logger.error(f"Error processing TXT file {file_path}: {e}")
            
            # Scan for EPUB files
            epub_files = list(novel_dir.glob('**/*.epub'))
            logger.info(f"Found {len(epub_files)} EPUB files")
            
            for file_path in epub_files:
                try:
                    self.handler._process_novel_file(str(file_path))
                except Exception as e:
                    logger.error(f"Error processing EPUB file {file_path}: {e}")
        
        logger.info("Initial file scan completed")

    def get_statistics(self) -> dict:
        """Get monitoring statistics."""
        stats = self.storage.get_statistics()
        stats.update({
            'monitored_directories': [str(d) for d in self.novel_dirs],
            'is_running': self._is_running,
            'pending_files': len(self.handler._pending_files)
        })
        return stats

"""
Modern EPUB parser with byte offset indexing and metadata extraction.
"""
import re
import logging
from pathlib import Path
from typing import Op<PERSON>, Tuple, List
import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup

from ..core.file_reader import FileReader
from ..core.hash_utils import hash_manager
from ..models.base import NovelMetadata, ChapterMetadata

logger = logging.getLogger(__name__)


class EpubParser:
    """
    Modern parser for EPUB files with metadata extraction and byte offset indexing.
    """

    def __init__(self):
        """Initialize the EPUB parser."""
        pass

    def parse_file(self, file_path: Path) -> Optional[NovelMetadata]:
        """
        Parse an EPUB file and extract metadata with virtual byte offsets.

        Args:
            file_path: Path to the EPUB file

        Returns:
            NovelMetadata: Novel metadata with chapters
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists() or not file_path.is_file():
                logger.error(f"File not found: {file_path}")
                return None

            # Get file info
            file_size, file_hash = FileReader.get_file_info(file_path)
            if not file_hash:
                logger.error(f"Could not calculate hash for: {file_path}")
                return None

            # Read EPUB
            book = epub.read_epub(str(file_path))

            # Extract metadata
            title, author, description = self._extract_metadata(book, file_path)

            # Extract chapters
            chapters = self._extract_chapters_with_offsets(book)

            # Create novel metadata
            novel_data = NovelMetadata(
                title=title,
                author=author,
                description=description,
                file_path=str(file_path),
                file_size=file_size,
                file_hash=file_hash,
                chapter_count=len(chapters),
                chapters=chapters
            )

            logger.info(f"Successfully parsed EPUB: {title} with {len(chapters)} chapters")
            return novel_data

        except Exception as e:
            logger.error(f"Error parsing EPUB file {file_path}: {str(e)}")
            return None

    def _extract_metadata(self, book: epub.EpubBook, file_path: Path) -> Tuple[str, Optional[str], Optional[str]]:
        """
        Extract title, author, and description from EPUB metadata.

        Args:
            book: EPUB book object
            file_path: Path to the file (fallback for title)

        Returns:
            tuple: (title, author, description)
        """
        # Extract title
        title_meta = book.get_metadata('DC', 'title')
        title = title_meta[0][0] if title_meta else file_path.stem

        # Extract author
        author_meta = book.get_metadata('DC', 'creator')
        author = author_meta[0][0] if author_meta else None

        # Extract description
        desc_meta = book.get_metadata('DC', 'description')
        description = desc_meta[0][0] if desc_meta else None

        # Fallback: try to extract from filename if no author in metadata
        if not author:
            author_pattern = re.compile(r'(.+)\s作者：(.+)')
            match = author_pattern.match(file_path.stem)
            if match:
                title = match.group(1).strip()
                author = match.group(2).strip()

        return title, author, description

    def _extract_chapters_with_offsets(self, book: epub.EpubBook) -> List[ChapterMetadata]:
        """
        Extract chapters with virtual byte offsets for EPUB content.

        Args:
            book: EPUB book object

        Returns:
            List: List of ChapterMetadata
        """
        chapters = []
        current_offset = 0

        # Check for introductory content
        intro_content = self._extract_intro_content(book)
        if intro_content:
            content_hash = hash_manager.get_content_hash(intro_content)
            content_bytes = len(intro_content.encode('utf-8'))

            chapters.append(ChapterMetadata(
                title='简介',
                start_offset=current_offset,
                end_offset=current_offset + content_bytes,
                chapter_index=0,
                content_hash=content_hash
            ))
            current_offset += content_bytes

        # Extract chapters from TOC or spine
        toc_chapters = self._process_toc_with_offsets(book, current_offset)
        if toc_chapters:
            chapters.extend(toc_chapters)
        else:
            spine_chapters = self._process_spine_with_offsets(book, current_offset)
            chapters.extend(spine_chapters)

        # If no chapters found, create single chapter
        if not chapters:
            content = self._extract_all_content(book)
            content_hash = hash_manager.get_content_hash(content)
            content_bytes = len(content.encode('utf-8'))

            chapters.append(ChapterMetadata(
                title='全文',
                start_offset=0,
                end_offset=content_bytes,
                chapter_index=0,
                content_hash=content_hash
            ))

        return chapters

    def _extract_intro_content(self, book: epub.EpubBook) -> Optional[str]:
        """
        Extract introductory content that appears before chapters.

        Args:
            book: EPUB book object

        Returns:
            str: Intro content or None
        """
        try:
            # Get first document item
            first_item = None
            for item in book.get_items():
                if item.get_type() == ebooklib.ITEM_DOCUMENT:
                    first_item = item
                    break

            if first_item:
                content = self._clean_html_content(first_item.get_content())
                # Check if this looks like intro content
                if (len(content) > 0 and
                    not any(marker in content.lower() for marker in ['chapter', '章', '节'])):
                    return content

        except Exception as e:
            logger.warning(f"Error extracting intro content: {e}")

        return None

    def _process_toc_with_offsets(self, book: epub.EpubBook, start_offset: int) -> list[ChapterMetadata]:
        """
        Process table of contents with virtual byte offsets.

        Args:
            book: EPUB book object
            start_offset: Starting byte offset

        Returns:
            list: List of ChapterMetadata
        """
        chapters = []
        current_offset = start_offset
        chapter_index = len([ch for ch in chapters if ch.title == '简介'])  # Account for intro

        try:
            toc = book.toc
            if not toc:
                return []

            for item in toc:
                if isinstance(item, tuple):
                    # Section with subsections
                    section_title, section_href, section_children = item

                    if section_href:
                        content = self._extract_item_content(book, section_href, section_title)
                        content_hash = hash_manager.get_content_hash(content)
                        content_bytes = len(content.encode('utf-8'))

                        chapters.append(ChapterMetadata(
                            title=section_title,
                            start_offset=current_offset,
                            end_offset=current_offset + content_bytes,
                            chapter_index=chapter_index,
                            content_hash=content_hash
                        ))
                        current_offset += content_bytes
                        chapter_index += 1

                    # Process children
                    if section_children:
                        child_chapters = self._process_toc_items(
                            book, section_children, current_offset, chapter_index
                        )
                        chapters.extend(child_chapters)
                        if child_chapters:
                            current_offset = child_chapters[-1].end_offset
                            chapter_index += len(child_chapters)

                elif isinstance(item, epub.Link):
                    content = self._extract_item_content(book, item.href, item.title)
                    content_hash = hash_manager.get_content_hash(content)
                    content_bytes = len(content.encode('utf-8'))

                    chapters.append(ChapterMetadata(
                        title=item.title,
                        start_offset=current_offset,
                        end_offset=current_offset + content_bytes,
                        chapter_index=chapter_index,
                        content_hash=content_hash
                    ))
                    current_offset += content_bytes
                    chapter_index += 1

        except Exception as e:
            logger.warning(f"Error processing TOC: {e}")

        return chapters

    def _process_toc_items(self, book: epub.EpubBook, items: list, start_offset: int, start_index: int) -> list[ChapterMetadata]:
        """
        Process TOC items recursively.

        Args:
            book: EPUB book object
            items: List of TOC items
            start_offset: Starting byte offset
            start_index: Starting chapter index

        Returns:
            list: List of ChapterMetadata
        """
        chapters = []
        current_offset = start_offset
        chapter_index = start_index

        for item in items:
            if isinstance(item, tuple):
                section_title, section_href, section_children = item

                if section_href:
                    content = self._extract_item_content(book, section_href, section_title)
                    content_hash = hash_manager.get_content_hash(content)
                    content_bytes = len(content.encode('utf-8'))

                    chapters.append(ChapterMetadata(
                        title=section_title,
                        start_offset=current_offset,
                        end_offset=current_offset + content_bytes,
                        chapter_index=chapter_index,
                        content_hash=content_hash
                    ))
                    current_offset += content_bytes
                    chapter_index += 1

                if section_children:
                    child_chapters = self._process_toc_items(
                        book, section_children, current_offset, chapter_index
                    )
                    chapters.extend(child_chapters)
                    if child_chapters:
                        current_offset = child_chapters[-1].end_offset
                        chapter_index += len(child_chapters)

            elif isinstance(item, epub.Link):
                content = self._extract_item_content(book, item.href, item.title)
                content_hash = hash_manager.get_content_hash(content)
                content_bytes = len(content.encode('utf-8'))

                chapters.append(ChapterMetadata(
                    title=item.title,
                    start_offset=current_offset,
                    end_offset=current_offset + content_bytes,
                    chapter_index=chapter_index,
                    content_hash=content_hash
                ))
                current_offset += content_bytes
                chapter_index += 1

        return chapters

    def _process_spine_with_offsets(self, book: epub.EpubBook, start_offset: int) -> list[ChapterMetadata]:
        """
        Process spine items when no TOC is available.

        Args:
            book: EPUB book object
            start_offset: Starting byte offset

        Returns:
            list: List of ChapterMetadata
        """
        chapters = []
        current_offset = start_offset
        chapter_index = len([ch for ch in chapters if ch.title == '简介'])  # Account for intro

        # Skip first item if it was processed as intro
        start_index = 1 if self._extract_intro_content(book) else 0

        for i, item_id in enumerate(book.spine):
            if i < start_index:
                continue

            item = book.get_item_with_id(item_id)
            if item and item.get_type() == ebooklib.ITEM_DOCUMENT:
                # Extract title from HTML
                soup = BeautifulSoup(item.get_content(), 'html.parser')
                title_tag = soup.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                title = title_tag.get_text().strip() if title_tag else f"Chapter {chapter_index + 1}"

                # Get content and remove title
                content = self._clean_html_content(item.get_content())
                content = self._remove_title_from_content(content, title)

                content_hash = hash_manager.get_content_hash(content)
                content_bytes = len(content.encode('utf-8'))

                chapters.append(ChapterMetadata(
                    title=title,
                    start_offset=current_offset,
                    end_offset=current_offset + content_bytes,
                    chapter_index=chapter_index,
                    content_hash=content_hash
                ))
                current_offset += content_bytes
                chapter_index += 1

        return chapters

    def _extract_item_content(self, book: epub.EpubBook, href: str, title: str) -> str:
        """
        Extract content from an EPUB item by href.

        Args:
            book: EPUB book object
            href: Item href/reference
            title: Chapter title

        Returns:
            str: Cleaned content
        """
        try:
            # Find item by href
            item = None
            for book_item in book.get_items():
                if book_item.get_name() == href or book_item.get_name().endswith(href):
                    item = book_item
                    break

            if not item:
                logger.warning(f"Could not find item for href: {href}")
                return ""

            # Clean HTML content
            content = self._clean_html_content(item.get_content())

            # Remove title from content to avoid duplication
            content = self._remove_title_from_content(content, title)

            return content

        except Exception as e:
            logger.warning(f"Error extracting content for {href}: {e}")
            return ""

    def _clean_html_content(self, html_content: bytes) -> str:
        """
        Clean HTML content and extract text.

        Args:
            html_content: Raw HTML content

        Returns:
            str: Cleaned text content
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Get text and clean up
            text = soup.get_text()

            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)

            return text

        except Exception as e:
            logger.warning(f"Error cleaning HTML content: {e}")
            return ""

    def _remove_title_from_content(self, content: str, title: str) -> str:
        """
        Remove chapter title from content to avoid duplication.

        Args:
            content: Chapter content
            title: Chapter title

        Returns:
            str: Content with title removed
        """
        if not title or not content:
            return content

        # Try to remove title from beginning of content
        content_lines = content.split('\n')
        if content_lines and title.strip() in content_lines[0]:
            content_lines = content_lines[1:]

        return '\n'.join(content_lines).strip()

    def _extract_all_content(self, book: epub.EpubBook) -> str:
        """
        Extract all text content from EPUB as fallback.

        Args:
            book: EPUB book object

        Returns:
            str: All content combined
        """
        all_content = []

        for item in book.get_items():
            if item.get_type() == ebooklib.ITEM_DOCUMENT:
                content = self._clean_html_content(item.get_content())
                if content:
                    all_content.append(content)

        return '\n\n'.join(all_content)

"""
Novel Parser - A modern, high-performance system for parsing and monitoring novel files.

Features:
- Byte-offset indexing for memory efficiency
- Incremental parsing for performance
- Support for TXT and EPUB formats
- FastAPI-based REST API
- Cross-platform file monitoring
"""

__version__ = "2.0.0"
__author__ = "Novel Parser Team"
__description__ = "Modern novel parsing system with byte-offset indexing"

from .storage import NovelStorage
from .parser import NovelParser, EpubParser, NovelMonitor
from .api import create_app
from .models import (
    NovelInfo,
    ChapterInfo,
    NovelChapters,
    ChapterContent,
    SearchResponse,
    StatusResponse,
)

__all__ = [
    "NovelStorage",
    "NovelParser", 
    "EpubParser",
    "NovelMonitor",
    "create_app",
    "NovelInfo",
    "ChapterInfo",
    "NovelChapters", 
    "ChapterContent",
    "SearchResponse",
    "StatusResponse",
]
